import React, {<PERSON>} from 'react';
import Froala from '../../../../Common/React/Froala/Froala.js';
import {useIdCard} from '../../../lib/contexts/AuthContext.js';
import {getEditorLang} from '../froalaTextEditors';
import {config} from './config';
import {ReportId} from '../../../../../types/TReport';


const options = {
    actionButtons: false,
    reactPlugins: [],
};

export const ReportTextEditor: FC<{ reportId: ReportId, content: string, placeholderText: string, onFocus: () => void, onBlur: () => void }> = ({reportId, content = '', placeholderText, onFocus, onBlur}) => {
    const idCard = useIdCard(),
        lang = getEditorLang(idCard.data.language);

    const initEditor = (editor) => editor.toolbar?.hide(),
        handleBlur = (e) => {
            //needed to remove the focus and to call config.onBlur when clicking on a chartItem
            if (document.activeElement && e.target.matches('[class*="highcharts"]')) {
                document.activeElement.blur();
            }
        };

    return (
        <Froala
            autofocus={false}
            content={content}
            options={options}
            // onInit={initEditor}
            onBlur={handleBlur}
            config={config(reportId, lang, placeholderText, onFocus, onBlur)}
        />
    );
};