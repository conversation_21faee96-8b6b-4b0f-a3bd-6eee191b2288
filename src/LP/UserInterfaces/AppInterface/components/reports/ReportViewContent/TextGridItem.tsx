import React, {useState} from 'react';
import './react.Reports.scss';
import {patchTextItem} from '../../../lib/api/ReportsAPI';
import {GridItemProps} from './ReportGridItem';
import {ReportTextEditor} from '../../FroalaTextEditors/ReportTextEditor/ReportTextEditor';
import {useTranslator} from '../../../lib/hooks/useTranslator';
import {IconButton} from '../../../elements/Buttons/IconButton';
import {ReportItemDeleteConfirm} from '../ReportItemDeleteConfirm/ReportItemDeleteConfirm';


export const TextGridItem = React.forwardRef<HTMLDivElement, GridItemProps>(({item, itemData, onChange, gridStatus, style, className, children, onMouseDown, onMouseUp, onTouchEnd}, ref) => {
    const [t] = useTranslator('ReportEditor'),
        [selected, setSelected] = useState(false);

    const handleFocus = () => {
            setSelected(true);
        },
        handlePatchReport = (_e, {text}) => {
            setSelected(false);
            return patchTextItem(itemData?.reportId, item?.i, {text});
        };

    return (

        <div style={{...style}} id={item?.i} className={`${className} ${gridStatus} ${selected ? 'selected' : ''} text-grid-item`} ref={ref} onMouseDown={onMouseDown} onMouseUp={onMouseUp} onTouchEnd={onTouchEnd} onClick={handleFocus}>

            <div className={'report-item-container'}>
                <ReportTextEditor reportId={itemData?.reportId}
                                  content={itemData?.text}
                                  placeholderText={'Please enter text here'}
                                  onFocus={handleFocus}
                                  onBlur={handlePatchReport}
                />

                {children}
            </div>

            <span className={'grid-item-buttons'}>

                <ReportItemDeleteConfirm key={'delete'}
                                         onDelete={onChange}
                                         reportId={itemData?.reportId}
                                         itemId={item?.i}
                                         trigger={<IconButton title={t('iconButton.deleteItem')}
                                                              icon={'trash alternate outline'}
                                                              size={'mini'}
                                                              noColor/>}/>
            </span>
        </div>
    );
});

TextGridItem.displayName = 'TextGridItem';