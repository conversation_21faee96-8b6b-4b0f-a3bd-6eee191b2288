import React, {useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>header, Icon, IconGroup, Loader} from 'semantic-ui-react';
import './react.Reports.scss';
import {createChartItem, createTextItem} from '../../../lib/api/ReportsAPI';
import {useSyncedState} from '../../../lib/hooks/useSyncedState';
import {GridItemProps} from './ReportGridItem';


export const EmptyGridItem = React.forwardRef<HTMLDivElement, GridItemProps>(({report, item, onChange, gridStatus, style, className, children}, ref) => {
    const [isLoading, setIsLoading] = useState(false),
        [showItem, setShowItem] = useSyncedState(false),
        showHandles = false;
    const handleAddTextItem = () => {
            setIsLoading(true);
            const position = {row: item.y, column: item.x, width: item.w, height: item.h};
            return createTextItem(report.id, {text: '', lang: report.lang, position}).then(onChange).finally(() => setIsLoading(false));
        },
        handleAddChartItem = () => {
            setIsLoading(true);
            const position = {row: item.y, column: item.x, width: item.w, height: item.h};
            return createChartItem(report.id, {lang: report.lang, position}).then(onChange).finally(() => setIsLoading(false));
        };

    //FYI: hover area to reduce the effect that empty grid item is shown when another item is resized
    return (
        <div style={{...style}} id={item.i} className={`${className} ${isLoading ? 'loading' : ''} ${gridStatus} ${showItem ? 'active' : ''} empty-grid-item`} ref={ref}>
            {isLoading ? <Loader active={isLoading}/>
                :
                <div className={'hover-area'} onMouseOver={() => setShowItem(true)} onMouseLeave={() => setShowItem(false)}>
                    <Header as="h4" icon onClick={handleAddTextItem}>
                        <IconGroup>
                            <Icon name="square outline" color={'grey'}/>
                            <Icon size="mini" name="align justify" color={'grey'}/>
                        </IconGroup>
                        <HeaderSubheader>
                            Text
                        </HeaderSubheader>
                    </Header>
                    <Header as="h4" icon onClick={handleAddChartItem}>
                        <Icon name={'chart bar outline'} color={'grey'}/>
                        <HeaderSubheader>
                            Diagramm
                        </HeaderSubheader>
                    </Header>
                </div>}
            {showHandles && children}
        </div>
    );
});

EmptyGridItem.displayName = 'EmptyGridItem';