@use "../../../../Common/Styles/_colors.scss";

.report-wrapper {
  border-style: dotted !important;


  .report-grid-item {
    border: 1px solid transparent;
    padding: 1.25em;
    z-index: 2;
    background-color: white;

    &.idle.react-grid-item.cssTransforms {
      transition-property: left, top, height;
    }


    .report-item-container {
      height: 100%;

      * {
        max-height: 100%;
      }

      //& > :not(:hover) [class~="react-resizable-handle"] {
      //  background-image: unset;
      //}

    }

    .grid-item-buttons {
      position: fixed;
      bottom: 0.5em;
      right: 0.2em;
      z-index: 10;
      visibility: hidden;
    }

    &:hover {
      cursor: grab;
      border-color: colors.$lightgrey;

      .grid-item-buttons {
        visibility: visible;
      }

      //& > .react-resizable-handle-ne, & > .react-resizable-handle-se, & > .react-resizable-handle-sw, & > .react-resizable-handle-nw {
      //
      //}
    }


    &.chart-grid-item {
      &:active {
        border: 1px dashed colors.$lightgrey;
        cursor: grabbing;
        -webkit-tap-highlight-color: #0d5aa7;
      }

      &.selected {
        border: 1px solid colors.$blue;
      }
    }


    &.text-grid-item {
      &.dragging:active {
        border: 1px dashed colors.$lightgrey;
        cursor: grabbing;
      }

      &.selected {
        border: 1px solid colors.$blue;
      }

      // needed to enable click on image on froala editor
      &.react-grid-item img {
        pointer-events: unset;
        user-select: unset;
      }

      .fr-box.fr-basic {
        height: fit-content;
        max-height: 100%;
        overflow-y: auto;
        z-index: 10;
        border-radius: 0;
        border: none;

        .fr-wrapper, .fr-toolbar, .fr-second-toolbar, .fr-element, .fr-view {
          border: 1px solid transparent;
        }

        .fr-element {
          padding: 2px !important;
        }
      }

      .fr-second-toolbar {
        display: none;
      }

      &:not(.dragging):hover .fr-wrapper:not(:focus-within) {
        border: 1px dashed colors.$lightgrey;
        z-index: 11;
      }

      &:not(.dragging) .fr-box.fr-basic .fr-wrapper:focus-within {
        border: 1px dashed colors.$blue;
        cursor: text;
      }
    }

    &.react-grid-placeholder {
      background-color: blue;
    }
  }

  .react-grid-item.report-grid-item.empty-grid-item {
    border: 1px solid colors.$lightgrey;
    opacity: 0;
    z-index: 1;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    transition: none;


    &.empty-report, &.loading {
      opacity: 0.5;
    }

    &.idle.active {
      background-color: transparent;
      opacity: 1;
    }

    .hover-area {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: space-evenly;
      align-items: center;
      width: 75%;
      height: 75%;
      min-width: fit-content;
      min-height: fit-content;
    }

    .ui.header {
      margin: 0;
      cursor: pointer;

      &:hover {
        i {
          color: colors.$blue;
        }
      }
    }
  }
}

#toolbarContainer {
  position: sticky;
  top: 1.5em;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;


  .fr-toolbar.fr-top {
    position: absolute;
    top: -0.75em;
    width: 90%;
    border-radius: 0;
    border-bottom: 1px solid transparent;

    .fr-newline {
      display: none;
    }
  }
}