import React from 'react';
import './react.Reports.scss';
import {EmptyGridItem} from './EmptyGridItem';
import {GridItem} from '../../../../../types/ReportGridTable';
import {Report} from '../../../../../types/TReport';
import * as Types from '../../../../../types/TReportItem';
import {GridStatus} from './ReportEditGrid';
import {TextGridItem} from './TextGridItem';
import {ChartGridItem} from './ChartGridItem';


export interface GridItemProps {
    report: Report;
    item: GridItem;
    itemData?: Types.ReportChartItem | Types.ReportTextItem;
    selectedItem?: string;
    onChange: () => void;
    onOpenChartEditor: (e: React.MouseEvent, id: string) => void;
    onFocusTextItem: (id: string) => void;
    gridStatus: GridStatus;
    style?: React.CSSProperties;
    className?: string;
    children?: React.ReactNode;
    onMouseDown?: React.MouseEventHandler<HTMLDivElement>;
    onMouseUp?: React.MouseEventHandler<HTMLDivElement>;
    onTouchEnd?: React.TouchEventHandler<HTMLDivElement>;
}


//Props are NEEDED; DO not remove. See https://github.com/react-grid-layout/react-grid-layout/tree/master?tab=readme-ov-file#custom-child-components-and-draggable-handles
export const ReportGridItem = React.forwardRef<HTMLDivElement, GridItemProps>(({item, itemData = {}, report, selectedItem, gridStatus, style, className, onMouseDown, onMouseUp, onTouchEnd, onChange, onOpenChartEditor, children, ...props}, ref) => {


    const gridItemProps = {
        onChange,
        report,
        item,
        itemData,
        gridStatus,
        style: {...style},
        className,
        ref,
        onMouseDown,
        onMouseUp,
        onTouchEnd,
        children,
        ...props
    };


    if (itemData.type === null) {
        return;
    }

    if (itemData.type === 'ph') {
        return <EmptyGridItem {...gridItemProps} />;
    }

    if (itemData.type === 'text') {
        return <TextGridItem {...gridItemProps} />;
    }

    if (itemData.type === 'chart') {
        return <ChartGridItem onOpenChartEditor={onOpenChartEditor} selectedItem={selectedItem} {...gridItemProps} />;
    }
});

ReportGridItem.displayName = 'ReportGridItem';
