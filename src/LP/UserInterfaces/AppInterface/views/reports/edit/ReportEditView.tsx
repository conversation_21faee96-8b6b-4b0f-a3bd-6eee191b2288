import React, {useState} from 'react';
import {useLocation, useNavigate, useParams} from 'react-router-dom';
import {fetchReport, fetchReportItems} from '../../../lib/api/ReportsAPI';
import {getDisplayItemsGrid} from '../../../components/reports/ReportViewContent/reportFunctions';
import {getPolls} from '../../../lib/api/PollsAPI';
import {useQueries} from '@tanstack/react-query';
import {useQueryOptionsDefaults} from '../../../lib/defaults/useQueryOptions';
import {Loader} from 'semantic-ui-react';
import {ReportSidebarContent} from './ReportSidebarContent';
import {StyledReportViewContentWrapper, StyledReportViewSidebarWrapper, StyledReportViewWrapper} from './StyledViewWrappers';
import {useEventListener} from '../../../lib/hooks/useEventListener';
import {useTranslator} from '../../../lib/hooks/useTranslator';
import {ReportViewContent} from '../../../components/reports/ReportViewContent/ReportViewContent';
import {PaddedContentLayout} from '../../../layouts/contentLayouts/PaddedContentLayout';
import {BackToReportsLink} from '../../../components/reports/ReportViewContent/BackToReportsLink';


//The height of the view header in pixels
const headerHeight = 47;
export default function ReportEditView({isPollReport = false}) {
    const [t] = useTranslator('ReportEditView'),
        {reportId, pollId} = useParams(),
        [isSidebarVisible, setIsSidebarVisible] = useState(false),
        [itemId, setItemId] = useState(null),
        [isScrolled, setIsScrolled] = useState(false),
        navigate = useNavigate(),
        {pathname} = useLocation(),
        endIndex = pathname.indexOf('edit'),
        redirectPath = endIndex > 0 ? pathname.slice(0, endIndex) : '/';

    useEventListener('scroll', () => setIsScrolled(window.scrollY > headerHeight));
    const loadReport = () => fetchReport(reportId).then(report => {
            if (pollId && !report.polls.includes(+pollId)) {
                navigate(redirectPath);
                return {};
            } else {
                return report;
            }
        }).catch(() => {
            navigate(redirectPath);
            return {};
        }),
        loadReportItems = () => fetchReportItems(reportId).then(getDisplayItemsGrid, true),
        loadPolls = () => getPolls({limit: 100});

    const [
        {data: report = {}, isLoading: isReportLoading},
        {data: items = [], isLoading: isItemsLoading, refetch: refetchReportItems, dataUpdatedAt},
        {data: polls = [], isLoading: isPollsLoading}] = useQueries({
        queries: [
            {queryKey: ['getReport', reportId, pollId], queryFn: loadReport, ...useQueryOptionsDefaults},
            {queryKey: ['getReportItems', reportId], queryFn: loadReportItems, ...useQueryOptionsDefaults},
            {queryKey: ['getAllPolls'], queryFn: loadPolls, ...useQueryOptionsDefaults}]
    });
    const handleOpenChartEditor = (e, id) => {
            e.stopPropagation();
            //TODO: Decide if toggle or not
            if (id === itemId) {
                setIsSidebarVisible(false);
                setItemId(null);
            } else {
                setIsSidebarVisible(true);
                setItemId(id);
            }
        },
        handleHideSidebar = (e) => {
            if (!e.target?.matches('[class*="highcharts"]')) {
                setIsSidebarVisible(false);
            }
        },
        handleDeleteChartItem = () => {
            setIsSidebarVisible(false);
            refetchReportItems();
        },
        handleTransitionEnd = (e) => {
            if (e.propertyName === 'transform' && !isSidebarVisible) {
                setItemId(null); //for the first condition, setting itemId to null is needed
            }
        };

    if (!report.id || isReportLoading || isItemsLoading || isPollsLoading) {
        return <Loader/>;
    }

    return (
        <StyledReportViewWrapper>
            <StyledReportViewContentWrapper isSidebarVisible={isSidebarVisible} onMouseUp={handleHideSidebar} onTouchEnd={handleHideSidebar}>
                <PaddedContentLayout documentTitle={t('docTitle.editReport')}>
                    <ReportViewContent report={report} items={items} dataUpdatedAt={dataUpdatedAt} refetchReportItems={refetchReportItems} selectedItem={itemId} onOpenChartEditor={handleOpenChartEditor}/>
                    {!isPollReport && <BackToReportsLink/>}
                </PaddedContentLayout>
            </StyledReportViewContentWrapper>
            <StyledReportViewSidebarWrapper isSidebarVisible={isSidebarVisible} isScrolled={isScrolled} onTransitionEnd={handleTransitionEnd}>
                <ReportSidebarContent itemId={itemId} polls={polls} report={report} onItemDelete={handleDeleteChartItem}/>
            </StyledReportViewSidebarWrapper>
        </StyledReportViewWrapper>
    );
};