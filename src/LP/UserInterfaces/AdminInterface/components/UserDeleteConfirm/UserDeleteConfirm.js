import React from 'react';
import {ConfirmModal} from "../../../AppInterface/elements/ConfirmModal/ConfirmModal";
import {useTranslator} from "../../../AppInterface/lib/hooks/useTranslator";
import {Segment} from "semantic-ui-react";

export const UserDeleteConfirm = ({user, onConfirm, ...props}) => {
    const [t] = useTranslator('UserDeleteConfirm');

    return <ConfirmModal titleIcon={'trash alternate outline'}
                         title={t('deleteUserConfirm.title')}
                         confirmIcon={'trash alternate outline'}
                         confirmLabel={t('words.Delete', {ns: 'common'})}
                         cancelLabel={t('words.Cancel', {ns: 'common'})}
                         onConfirm={onConfirm}
                         {...props}
    >
        <Segment basic className={'horizontally fitted'}>
            {t('deleteUserConfirm.message', {name: user.name, email: user.email})}
        </Segment>
    </ConfirmModal>;
};
