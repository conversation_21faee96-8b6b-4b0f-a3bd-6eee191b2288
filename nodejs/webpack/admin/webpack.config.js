const {merge} = require('webpack-merge');
const path = require('path');
const common = require('../webpack.common.js');

let webpackConfig = merge(common, {
    entry: {
        admin: ['core-js/stable', 'regenerator-runtime/runtime', path.resolve(__dirname, '../../../src/LP/UserInterfaces/AdminInterface/index.js')] //add polyfills
    },

    output: {
        path: path.resolve(__dirname, '../../../public/assets/admin/app/'),
        publicPath: '/assets/admin/app/'
    }
});

// add sourcefile upload to sentry, if all necessary env vars are set
const sentryEnv = require('../../sentry.env.js');
if (sentryEnv.available) {
    webpackConfig.plugins.push(sentryEnv.getPlugin(webpackConfig.output.path));
}

module.exports = webpackConfig;